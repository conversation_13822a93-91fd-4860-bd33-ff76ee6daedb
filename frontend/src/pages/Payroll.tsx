import { CloseOutlined, DeleteOutlined, EditOutlined, SaveOutlined } from '@ant-design/icons';
import {
  Tag as AntdTag,
  Button,
  DatePicker,
  Form,
  FormInstance,
  Input,
  InputNumber,
  message,
  Popconfirm,
  Select,
  Space,
  Table,
  Typography,
} from 'antd';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { DATE_FORMAT } from '../constants/dateFormats';
import ClearFiltersButton from '../components/common/clearFiltersButton';
import { CurrencyInput } from '../components/common/CurrencyInput';
import { FormCard } from '../components/common/FormCard';
import { LoadingTable } from '../components/common/LoadingTable';
import { useOrganization } from '../contexts/OrganizationContext';
import { useTableControls } from '../hooks/useTableControls';
import {
  addPayrollExpense,
  createTag,
  deletePayrollExpense,
  getPayroll,
  getTags,
  updatePayrollExpense,
} from '../services/api';
import { PayrollExpense, Tag, UpsertPayrollExpense } from '../types';
import { TableColumnType } from '../types/table';
import { formatCurrency, formatRecurrence, parseCurrency } from '../utils/helpers';
import {
  getColumnSearchProps,
  getDateRangeSearchProps,
  getNumericColumnSearchProps,
  getTagColumnSearchProps,
} from '../utils/tableFilters';

const { Title } = Typography;
const { Option } = Select;

const Payroll: React.FC = () => {
  const [form] = Form.useForm<UpsertPayrollExpense>();
  const [expenses, setExpenses] = useState<PayrollExpense[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [submitting, setSubmitting] = useState<boolean>(false);
  const [editingKey, setEditingKey] = useState<string | number>('');
  const [editForm] = Form.useForm<UpsertPayrollExpense>();
  const [total, setTotal] = useState<number>(0);
  const [allTags, setAllTags] = useState<Tag[]>([]);
  const [tagOptions, setTagOptions] = useState<{ label: string; value: string }[]>([]);
  const { filteredInfo, sortedInfo, handleTableChange, clearFiltersAndSorting } = useTableControls({
    initialSort: { columnKey: 'date', order: 'descend' },
  });
  const { organization } = useOrganization();

  useEffect(() => {
    fetchExpenses();
    fetchAllTags();
  }, []);

  useEffect(() => {
    const sum = expenses.reduce((acc, expense) => acc + expense.amount, 0);
    setTotal(sum);
  }, [expenses]);

  // Add CSS for table cell alignment
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
      .payroll-table .ant-table-tbody > tr > td {
        vertical-align: top !important;
        padding-top: 8px !important;
      }
      .payroll-table .ant-form-item {
        margin-bottom: 0 !important;
      }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const fetchAllTags = async () => {
    try {
      const response = await getTags();
      const sortedTags = response.data.sort((a: Tag, b: Tag) => a.name.localeCompare(b.name));
      setAllTags(sortedTags);
      setTagOptions(
        sortedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );
    } catch (error: any) {
      console.error('Error fetching tags:', error);
      message.error('Failed to load tags');
    }
  };

  const fetchExpenses = async (): Promise<void> => {
    try {
      setLoading(true);
      const response = await getPayroll();
      const expensesData = Array.isArray(response.data) ? response.data : [];
      setExpenses(expensesData);
    } catch (error) {
      console.error('Error fetching payroll expenses:', error);
      message.error('Failed to load payroll expenses');
      setExpenses([]);
    } finally {
      setLoading(false);
    }
  };

  const processTags = async (tags: string[] | undefined): Promise<number[]> => {
    if (!organization) {
      message.error('Organization not found');
      return [];
    }
    try {
      if (!tags) {
        return [];
      }
      const tagIds: number[] = [];
      const newTags: Tag[] = [];
      for (const tagName of tags) {
        const existingTag = allTags.find(tag => tag.name === tagName);
        if (existingTag) {
          tagIds.push(existingTag.id);
        } else {
          const trimmedTagName = tagName.trim();
          const response = await createTag({
            id: 0,
            name: trimmedTagName,
            organization_id: organization.id,
          });
          const tagObject = response.data;
          if (tagObject) {
            tagIds.push(tagObject.id);
            newTags.push(tagObject);
          }
        }
      }

      const updatedTags = [...allTags, ...newTags].sort((a, b) => a.name.localeCompare(b.name));
      setAllTags(updatedTags);
      setTagOptions(
        updatedTags.map(tag => ({
          label: tag.name,
          value: tag.name,
        }))
      );

      return tagIds;
    } catch (error) {
      console.error('Error processing tags:', error);
      message.error('Failed to process tags');
      return [];
    }
  };

  const handleSubmit = async (values: UpsertPayrollExpense): Promise<void> => {
    try {
      setSubmitting(true);
      const tagIds = await processTags(values.tag_names);
      const { tags, tag_names, ...rest } = values;
      const formattedValues = {
        ...rest,
        amount: parseCurrency(values.amount),
        tag_ids: tagIds,
      };
      await addPayrollExpense(formattedValues as UpsertPayrollExpense);
      message.success('Payroll expense added successfully');
      form.resetFields();
      await fetchExpenses();
    } catch (error) {
      console.error('Error adding payroll expense:', error);
      message.error('Failed to add payroll expense');
    } finally {
      setSubmitting(false);
    }
  };

  const handleDelete = async (id: string | number): Promise<void> => {
    try {
      await deletePayrollExpense(id);
      message.success('Payroll expense deleted successfully');
      await fetchExpenses();
    } catch (error) {
      console.error('Error deleting payroll expense:', error);
      message.error('Failed to delete payroll expense');
    }
  };

  const isEditing = (record: PayrollExpense): boolean => record.id === editingKey;

  const edit = (record: PayrollExpense): void => {
    editForm.setFieldsValue({
      ...record,
      recurrence: record.recurrence ?? 'None',
      tag_names: record.tags ? record.tags.map(tag => tag.name) : [],
    });
    setEditingKey(record.id);
  };

  const cancel = (): void => {
    setEditingKey('');
  };

  const save = async (id: string | number): Promise<void> => {
    try {
      const row = (await editForm.validateFields()) as UpsertPayrollExpense;
      const tagIds = await processTags(row.tag_names);
      const { tags, tag_names, id: rowId, ...rest } = row;
      const updatedExpense = {
        id: id as number,
        ...rest,
        tag_ids: tagIds,
      };

      await updatePayrollExpense(updatedExpense as UpsertPayrollExpense);
      message.success('Payroll expense updated successfully');
      setEditingKey('');
      await fetchExpenses();
    } catch (errInfo) {
      console.log('Validate Failed:', errInfo);
      message.error('Failed to update expense. Please check the form fields.');
    }
  };

  const handleRecurrenceChange = (value: string, formInstance: FormInstance): void => {
    if (value !== 'Custom') {
      formInstance.setFieldsValue({ interval: undefined });
    }
    // Clear week/day fields if not monthly or biweekly
    if (value !== 'Monthly' && value !== 'Bi-weekly') {
      formInstance.setFieldsValue({
        week_of_month: undefined,
        day_of_week: undefined,
      });
    }
  };

  const columns: TableColumnType<PayrollExpense>[] = [
    {
      title: 'Date',
      dataIndex: 'date',
      key: 'date',
      width: '12%',
      sorter: (a, b) => a.date.unix() - b.date.unix(),
      ...getDateRangeSearchProps('date'),
      defaultSortOrder: 'descend' as const,
      render: (date: dayjs.Dayjs, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='date' rules={[{ required: true }]}>
            <DatePicker format={DATE_FORMAT} style={{ width: '100%' }} />
          </Form.Item>
        ) : (
          date.format(DATE_FORMAT)
        );
      },
    },
    {
      title: 'Employee Name',
      dataIndex: 'employee_name',
      key: 'employee_name',
      width: '18%',
      ...getColumnSearchProps('employee_name'),
      render: (text: string, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='employee_name' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        ) : (
          text
        );
      },
    },
    {
      title: 'Amount',
      dataIndex: 'amount',
      key: 'amount',
      width: '12%',
      sorter: (a, b) => a.amount - b.amount,
      ...getNumericColumnSearchProps('amount'),
      render: (amount: number, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='amount' rules={[{ required: true }]}>
            <CurrencyInput />
          </Form.Item>
        ) : (
          formatCurrency(amount)
        );
      },
    },
    {
      title: 'Recurrence',
      dataIndex: 'recurrence',
      key: 'recurrence',
      width: '12%',
      render: (recurrence: string, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='recurrence'>
            <Select onChange={value => handleRecurrenceChange(value, editForm)} allowClear>
              <Option value='None'>None</Option>
              <Option value='Weekly'>Weekly</Option>
              <Option value='Bi-weekly'>Bi-weekly</Option>
              <Option value='Monthly'>Monthly</Option>
              <Option value='Quarterly'>Quarterly</Option>
              <Option value='Custom'>Custom</Option>
            </Select>
          </Form.Item>
        ) : (
          formatRecurrence(recurrence, record.interval)
        );
      },
    },
    {
      title: 'Interval (days)',
      dataIndex: 'interval',
      key: 'interval',
      width: '10%',
      render: (interval: number, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() => (
              <Form.Item name='interval'>
                <InputNumber
                  disabled={editForm.getFieldValue('recurrence') !== 'Custom'}
                  min={1}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            )}
          </Form.Item>
        ) : (
          interval
        );
      },
    },
    {
      title: 'Week of Month',
      dataIndex: 'week_of_month',
      key: 'week_of_month',
      width: '10%',
      render: (week_of_month: number, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() => (
              <Form.Item name='week_of_month'>
                <Select
                  disabled={
                    !['Monthly', 'Bi-weekly'].includes(editForm.getFieldValue('recurrence'))
                  }
                  placeholder='Week'
                  style={{ width: '100%' }}
                  allowClear
                >
                  <Option value={1}>1st</Option>
                  <Option value={2}>2nd</Option>
                  <Option value={3}>3rd</Option>
                  <Option value={4}>4th</Option>
                </Select>
              </Form.Item>
            )}
          </Form.Item>
        ) : week_of_month ? (
          `${week_of_month}${
            week_of_month === 1
              ? 'st'
              : week_of_month === 2
              ? 'nd'
              : week_of_month === 3
              ? 'rd'
              : 'th'
          }`
        ) : (
          ''
        );
      },
    },
    {
      title: 'Day of Week',
      dataIndex: 'day_of_week',
      key: 'day_of_week',
      width: '10%',
      render: (day_of_week: number, record: PayrollExpense) => {
        const editable = isEditing(record);
        const dayNames = [
          'Monday',
          'Tuesday',
          'Wednesday',
          'Thursday',
          'Friday',
          'Saturday',
          'Sunday',
        ];
        return editable ? (
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() => (
              <Form.Item name='day_of_week'>
                <Select
                  disabled={
                    !['Monthly', 'Bi-weekly'].includes(editForm.getFieldValue('recurrence'))
                  }
                  placeholder='Day'
                  style={{ width: '100%' }}
                  allowClear
                >
                  {dayNames.map((day, index) => (
                    <Option key={index} value={index}>
                      {day}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </Form.Item>
        ) : day_of_week !== undefined ? (
          dayNames[day_of_week]
        ) : (
          ''
        );
      },
    },
    {
      title: 'Notes',
      dataIndex: 'notes',
      key: 'notes',
      width: '15%',
      render: (notes: string, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='notes'>
            <Input.TextArea rows={1} />
          </Form.Item>
        ) : (
          notes
        );
      },
    },
    {
      title: 'Tags',
      dataIndex: 'tags',
      key: 'tags',
      width: '13%',
      ...getTagColumnSearchProps(allTags),
      render: (tags: Tag[], record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Form.Item name='tag_names'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
        ) : (
          <>
            {(tags || []).map(tag => (
              <AntdTag color='blue' key={tag.id}>
                {tag.name}
              </AntdTag>
            ))}
          </>
        );
      },
    },
    {
      title: 'Actions',
      key: 'actions',
      width: '10%',
      render: (_: any, record: PayrollExpense) => {
        const editable = isEditing(record);
        return editable ? (
          <Space>
            <Button
              icon={<SaveOutlined />}
              type='primary'
              onClick={() => save(record.id)}
              size='small'
            >
              Save
            </Button>
            <Button icon={<CloseOutlined />} onClick={cancel} size='small'>
              Cancel
            </Button>
          </Space>
        ) : (
          <Space>
            <Button
              icon={<EditOutlined />}
              type='primary'
              disabled={editingKey !== ''}
              onClick={() => edit(record)}
              size='small'
            >
              Edit
            </Button>
            <Popconfirm title='Sure to delete?' onConfirm={() => handleDelete(record.id)}>
              <Button danger icon={<DeleteOutlined />} size='small' disabled={editingKey !== ''}>
                Delete
              </Button>
            </Popconfirm>
          </Space>
        );
      },
    },
  ];

  return (
    <div>
      <Title level={2}>Payroll</Title>
      <FormCard title='Add New Payroll Expense'>
        <Form form={form} onFinish={handleSubmit} layout='vertical'>
          <Form.Item name='date' label='Date' rules={[{ required: true }]}>
            <DatePicker style={{ width: '100%' }} />
          </Form.Item>
          <Form.Item name='employee_name' label='Employee Name' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name='amount' label='Amount' rules={[{ required: true }]}>
            <CurrencyInput />
          </Form.Item>
          <Form.Item name='recurrence' label='Recurrence' initialValue='None'>
            <Select onChange={value => handleRecurrenceChange(value, form)} allowClear>
              <Option value='None'>None</Option>
              <Option value='Weekly'>Weekly</Option>
              <Option value='Bi-weekly'>Bi-weekly</Option>
              <Option value='Monthly'>Monthly</Option>
              <Option value='Quarterly'>Quarterly</Option>
              <Option value='Custom'>Custom</Option>
            </Select>
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() =>
              form.getFieldValue('recurrence') === 'Custom' ? (
                <Form.Item name='interval' label='Interval (days)' rules={[{ required: true }]}>
                  <InputNumber min={1} style={{ width: '100%' }} />
                </Form.Item>
              ) : null
            }
          </Form.Item>
          <Form.Item
            noStyle
            shouldUpdate={(prevValues, currentValues) =>
              prevValues.recurrence !== currentValues.recurrence
            }
          >
            {() =>
              ['Monthly', 'Bi-weekly'].includes(form.getFieldValue('recurrence')) ? (
                <div style={{ display: 'flex', gap: '16px' }}>
                  <Form.Item name='week_of_month' label='Week of Month' style={{ flex: 1 }}>
                    <Select placeholder='Select week' allowClear>
                      <Option value={1}>1st</Option>
                      <Option value={2}>2nd</Option>
                      <Option value={3}>3rd</Option>
                      <Option value={4}>4th</Option>
                    </Select>
                  </Form.Item>
                  <Form.Item name='day_of_week' label='Day of Week' style={{ flex: 1 }}>
                    <Select placeholder='Select day' allowClear>
                      <Option value={0}>Monday</Option>
                      <Option value={1}>Tuesday</Option>
                      <Option value={2}>Wednesday</Option>
                      <Option value={3}>Thursday</Option>
                      <Option value={4}>Friday</Option>
                      <Option value={5}>Saturday</Option>
                      <Option value={6}>Sunday</Option>
                    </Select>
                  </Form.Item>
                </div>
              ) : null
            }
          </Form.Item>
          <Form.Item name='notes' label='Notes'>
            <Input.TextArea />
          </Form.Item>
          <Form.Item name='tag_names' label='Tags'>
            <Select
              mode='tags'
              style={{ width: '100%' }}
              placeholder='Select or create tags'
              tokenSeparators={[',']}
              options={tagOptions}
            />
          </Form.Item>
          <Form.Item>
            <Button type='primary' htmlType='submit' loading={submitting}>
              Add Expense
            </Button>
          </Form.Item>
        </Form>
      </FormCard>

      <FormCard title='Existing Payroll Expenses'>
        <Form form={editForm} component={false}>
          <ClearFiltersButton onClear={clearFiltersAndSorting} />
          <LoadingTable<PayrollExpense>
            loading={loading}
            columns={columns}
            dataSource={expenses}
            rowKey='key'
            pagination={{ defaultPageSize: 10, showSizeChanger: true }}
            onChange={handleTableChange}
            className='payroll-table'
            summary={() => (
              <Table.Summary fixed>
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={2} align='right'>
                    <strong>Total:</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={2}>
                    <strong>{formatCurrency(total)}</strong>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={3} />
                </Table.Summary.Row>
              </Table.Summary>
            )}
          />
        </Form>
      </FormCard>
    </div>
  );
};

export default Payroll;
