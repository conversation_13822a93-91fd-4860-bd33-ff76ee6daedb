import React from 'react';
import { Table, Typography } from 'antd';
import { formatCurrency } from '../utils/formatting';
import { formatDateRange } from '../utils/dateFormatting';
import dayjs from 'dayjs';
import { ArrowUpOutlined, ArrowDownOutlined } from '@ant-design/icons';
import { OverheadData } from '../types';

interface OverheadTableProps {
  overheadData: OverheadData | null;
  loading: boolean;
}

interface TableRow {
  key: string;
  category: string;
  [weekDate: string]: string | number;
}

const OverheadTable: React.FC<OverheadTableProps> = ({ overheadData, loading }) => {
  if (!overheadData) return null;

  const columns = [
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      width: 200,
      fixed: 'left' as const,
    },
    ...overheadData.labels.map((weekDate: string, index: number) => ({
      title: formatDateRange(dayjs(weekDate), dayjs(weekDate).day(6)),
      dataIndex: weekDate,
      key: weekDate,
      width: 250,
      render: (value: number, record: TableRow) => {
        // Always show the first column's value
        if (index === 0) {
          return formatCurrency(value);
        }
        if (value !== 0) {
          return formatCurrency(value);
        }

        return null;
      },
    })),
  ];

  const data: TableRow[] = [
    {
      key: 'accounts',
      category: 'Accounts Total',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.accounts_totals[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'payroll',
      category: 'Payroll Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.payroll_expenses_totals[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'fixed',
      category: 'Fixed Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.fixed_expenses_totals[index] || 0;
          return acc;
        },
        {}
      ),
    },
    {
      key: 'misc',
      category: 'Misc Expenses',
      ...overheadData.labels.reduce(
        (acc: Record<string, number>, weekDate: string, index: number) => {
          acc[weekDate] = overheadData.misc_expenses_totals[index] || 0;
          return acc;
        },
        {}
      ),
    },
  ];

  return (
    <div className='overhead-table'>
      <Typography.Title level={4}>Overhead</Typography.Title>
      <Table
        columns={columns}
        dataSource={data}
        loading={loading}
        pagination={false}
        scroll={{ x: 1800, y: 600 }}
        bordered
        size='small'
      />
    </div>
  );
};

export default OverheadTable;
