from datetime import datetime
from typing import Optional, List, TYPE_CHECKING
from sqlmodel import SQLModel, Field, Relationship
from pydantic import validator, model_validator
from sqlalchemy import Identity, BigInteger
from .utils import parse_amount, parse_date_util
from .tag import TagResponse, TagMappingTable

if TYPE_CHECKING:
    from .tag import TagTable


class FixedExpenseBase(SQLModel):
    date: datetime
    description: str
    amount: float
    recurrence: Optional[str] = None
    recurrence_interval: Optional[int] = None
    week_of_month: Optional[int] = None  # 1-4 for monthly/biweekly recurrence
    day_of_week: Optional[int] = None  # 0-6 (Monday=0) for monthly/biweekly recurrence

    @validator("date", pre=True)
    def parse_date(cls, v):
        return parse_date_util(v)

    @validator("amount", pre=True)
    def parse_expense_amount(cls, v):
        return parse_amount(v) if v is not None else 0.0

    @validator("recurrence_interval", pre=True)
    def parse_interval(cls, v):
        if isinstance(v, str) and v.strip():
            return int(v)
        return v

    @validator("week_of_month", pre=True)
    def parse_week_of_month(cls, v):
        if v is None or v == "":
            return None
        if isinstance(v, str) and v.strip():
            return int(v)
        return v

    @validator("day_of_week", pre=True)
    def parse_day_of_week(cls, v):
        if v is None or v == "":
            return None
        if isinstance(v, str) and v.strip():
            return int(v)
        return v

    @model_validator(mode="after")
    def process_values(self):
        if self.recurrence == "Custom" and self.recurrence_interval is None:
            self.recurrence_interval = 0

        # Validate week_of_month and day_of_week for monthly/biweekly recurrence
        if self.recurrence and self.recurrence.lower() in [
            "monthly",
            "bi-weekly",
            "biweekly",
        ]:
            if self.week_of_month is not None and (
                self.week_of_month < 1 or self.week_of_month > 4
            ):
                raise ValueError("week_of_month must be between 1 and 4")
            if self.day_of_week is not None and (
                self.day_of_week < 0 or self.day_of_week > 6
            ):
                raise ValueError("day_of_week must be between 0 and 6 (Monday=0)")

        return self


class FixedExpenseTable(FixedExpenseBase, table=True):
    __tablename__ = "fixed_expenses"
    id: Optional[int] = Field(
        sa_type=BigInteger,
        default=None,
        primary_key=True,
        nullable=False,
        sa_column_kwargs={"server_default": Identity()},
    )
    organization_id: int = Field(sa_type=BigInteger, foreign_key="organizations.id")
    tags: List["TagTable"] = Relationship(
        back_populates="fixed_expenses",
        link_model=TagMappingTable,
        sa_relationship_kwargs={
            "primaryjoin": "and_(FixedExpenseTable.id == TagMappingTable.entity_id, "
            "TagMappingTable.entity_type == 'fixed_expense')",
            "secondaryjoin": "TagTable.id == TagMappingTable.tag_id",
        },
    )


class FixedExpenseUpsert(FixedExpenseBase):
    id: Optional[int] = None
    organization_id: Optional[int] = None
    tag_ids: Optional[List[int]] = []


class FixedExpenseResponse(FixedExpenseBase):
    id: int
    organization_id: int
    tags: List["TagResponse"] = []
