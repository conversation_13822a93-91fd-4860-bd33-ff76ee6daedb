from datetime import datetime, timedelta
from sqlalchemy.ext.asyncio import AsyncSession
from app.cash_flow.db_operations import (
    AccountOperations,
    FixedExpenseOperations,
    MiscExpenseOperations,
    PayrollExpenseOperations,
    ProjectOperations,
    PurchaseOrderOperations,
    InvoiceOperations,
)
from app.cash_flow.models import (
    ForecastResponse,
    FixedExpenseTable,
)
from app.cash_flow.services.project_utils import create_project_response


class ForecastService:
    def __init__(self, session: AsyncSession):
        self.account_ops = AccountOperations(session)
        self.fixed_expense_ops = FixedExpenseOperations(session)
        self.misc_expense_ops = MiscExpenseOperations(session)
        self.payroll_expense_ops = PayrollExpenseOperations(session)
        self.project_ops = ProjectOperations(session)
        self.purchase_order_ops = PurchaseOrderOperations(session)
        self.invoice_ops = InvoiceOperations(session)

    def _ensure_naive_datetime(self, dt: datetime) -> datetime:
        """Ensure datetime object is naive (no timezone info)."""
        if dt is None:
            return dt
        if dt.tzinfo is not None:
            return dt.replace(tzinfo=None)
        return dt

    def _should_apply_recurring_expense(
        self, expense: FixedExpenseTable, week_start: datetime, week_end: datetime
    ):
        """
        Helper function to determine if a recurring expense should be applied in a given week
        based on its recurrence pattern.
        """
        # Ensure all datetime objects are naive
        week_start = self._ensure_naive_datetime(week_start)
        week_end = self._ensure_naive_datetime(week_end)
        expense_date = self._ensure_naive_datetime(expense.date)

        # Check if the week is on or after the expense date
        if week_end < expense_date:
            return False

        if not expense.recurrence:
            # Non-recurring: apply only if date falls within the week
            return expense_date >= week_start and expense_date <= week_end

        if expense.recurrence.lower() == "weekly":
            # Apply every week
            return True

        if expense.recurrence.lower() in ["bi-weekly", "biweekly"]:
            # Apply every other week
            weeks_since_start = (week_start - expense_date).days // 7
            return weeks_since_start % 2 == 0

        if expense.recurrence.lower() == "monthly":
            # Apply on approximately the same day each month (within the week)
            days_diff = (week_start - expense_date).days
            return (
                days_diff % 30 < 7
                and expense_date.day <= week_start.day <= expense_date.day + 6
            )

        if expense.recurrence.lower() == "quarterly":
            # Apply every 3 months on approximately the same day
            days_diff = (week_start - expense_date).days
            return (
                days_diff % 90 < 7
                and expense_date.day <= week_start.day <= expense_date.day + 6
            )

        if expense.recurrence.lower() == "1st & 3rd fridays":
            # Apply on 1st and 3rd Fridays of each month
            first_day = week_start.replace(day=1)
            first_friday = first_day + timedelta(days=(4 - first_day.weekday()) % 7)
            third_friday = first_friday + timedelta(days=14)
            return (week_start <= first_friday <= week_end) or (
                week_start <= third_friday <= week_end
            )

        if expense.recurrence.lower() == "custom" and expense.recurrence_interval:
            # Apply based on custom interval in days
            days_since_start = (week_start - expense_date).days
            return days_since_start % expense.recurrence_interval == 0

        return False

    async def _calculate_historical_totals(
        self, organization_id: int, start_date: datetime
    ) -> tuple[float, float, float]:
        """
        Calculate cumulative totals for all expenses, invoices, and purchase orders
        that occurred before the forecast start date.

        Returns:
            tuple: (payroll_total, fixed_total, misc_total)
        """
        # Get all data
        fixed_expenses = await self.fixed_expense_ops.get_fixed_expenses(
            organization_id
        )
        misc_expenses = await self.misc_expense_ops.get_misc_expenses(organization_id)
        payroll_expenses = await self.payroll_expense_ops.get_payroll_expenses(
            organization_id
        )

        payroll_total = 0.0
        fixed_total = 0.0
        misc_total = 0.0

        start_date = self._ensure_naive_datetime(start_date)

        # Calculate payroll expenses before start date
        for expense in payroll_expenses:
            expense_date = self._ensure_naive_datetime(expense.date)
            if expense_date < start_date:
                if expense.recurrence and expense.recurrence.lower() == "weekly":
                    # Calculate how many weeks this expense occurred before start date
                    weeks_diff = (start_date - expense_date).days // 7
                    payroll_total += float(expense.amount) * weeks_diff
                elif expense.recurrence and expense.recurrence.lower() in [
                    "bi-weekly",
                    "biweekly",
                ]:
                    # Calculate bi-weekly occurrences
                    weeks_diff = (start_date - expense_date).days // 7
                    payroll_total += float(expense.amount) * (weeks_diff // 2)
                elif expense.recurrence and expense.recurrence.lower() == "monthly":
                    # Approximate monthly occurrences (4.33 weeks per month)
                    weeks_diff = (start_date - expense_date).days // 7
                    payroll_total += float(expense.amount) * (weeks_diff // 4)
                elif not expense.recurrence:
                    # One-time expense
                    payroll_total += float(expense.amount)

        # Calculate fixed expenses before start date
        for expense in fixed_expenses:
            expense_date = self._ensure_naive_datetime(expense.date)
            if expense_date < start_date:
                if expense.recurrence and expense.recurrence.lower() == "weekly":
                    weeks_diff = (start_date - expense_date).days // 7
                    fixed_total += float(expense.amount) * weeks_diff
                elif expense.recurrence and expense.recurrence.lower() in [
                    "bi-weekly",
                    "biweekly",
                ]:
                    weeks_diff = (start_date - expense_date).days // 7
                    fixed_total += float(expense.amount) * (weeks_diff // 2)
                elif expense.recurrence and expense.recurrence.lower() == "monthly":
                    weeks_diff = (start_date - expense_date).days // 7
                    fixed_total += float(expense.amount) * (weeks_diff // 4)
                elif not expense.recurrence:
                    fixed_total += float(expense.amount)

        # Calculate misc expenses before start date (these are always one-time)
        for expense in misc_expenses:
            expense_date = self._ensure_naive_datetime(expense.date)
            if expense_date < start_date:
                misc_total += float(expense.amount)

        return payroll_total, fixed_total, misc_total

    async def _calculate_historical_project_data(
        self, organization_id: int, start_date: datetime
    ) -> tuple[float, float, float, float, float, float, float, float, float, float]:
        """
        Calculate cumulative totals for project milestones, expenses, purchase orders,
        and invoices that occurred before the forecast start date.

        Returns:
            tuple: (
                total_current_project_income,
                total_anticipated_project_income,
                total_current_project_expenses,
                total_anticipated_project_expenses,
                total_current_project_purchase_orders,
                total_anticipated_project_purchase_orders,
                total_current_project_invoices,
                total_anticipated_project_invoices,
            ) - effects on accounts
        """
        # Get all project data
        current_projects = await self.project_ops.get_current_projects(organization_id)
        anticipated_projects = await self.project_ops.get_anticipated_projects(
            organization_id
        )
        purchase_orders = await self.purchase_order_ops.get_purchase_orders(
            organization_id=organization_id
        )
        invoices = await self.invoice_ops.get_invoices(organization_id=organization_id)

        total_current_project_income = 0.0
        total_anticipated_project_income = 0.0
        total_current_project_savings = 0.0
        total_anticipated_project_savings = 0.0
        total_current_project_expenses = 0.0
        total_anticipated_project_expenses = 0.0
        total_current_project_purchase_orders = 0.0
        total_anticipated_project_purchase_orders = 0.0
        total_current_project_invoices = 0.0
        total_anticipated_project_invoices = 0.0

        start_date = self._ensure_naive_datetime(start_date)
        current_project_ids = {proj.id for proj in current_projects}
        anticipated_project_ids = {proj.id for proj in anticipated_projects}

        # Process current projects
        for project in current_projects:
            current_project_ids.add(project.id)
            # Process milestones (income)
            for milestone in project.milestones:
                milestone_date = self._ensure_naive_datetime(milestone.date)
                if milestone_date < start_date:
                    amount = float(milestone.amount)
                    savings_amount = amount * (project.savings_percentage / 100)
                    total_current_project_savings += savings_amount
                    # Only the non-savings portion affects accounts totals
                    total_current_project_income += amount

            # Process project expenses
            for cost in project.project_expenses:
                cost_date = self._ensure_naive_datetime(cost.date)
                if cost_date < start_date:
                    total_current_project_expenses += float(cost.amount)

        # Process anticipated projects
        for project in anticipated_projects:
            anticipated_project_ids.add(project.id)
            # Process milestones (income)
            for milestone in project.milestones:
                milestone_date = self._ensure_naive_datetime(milestone.date)
                if milestone_date < start_date:
                    amount = float(milestone.amount)
                    savings_amount = amount * (project.savings_percentage / 100)
                    total_anticipated_project_savings += savings_amount
                    total_anticipated_project_income += amount

            # Process project expenses
            for cost in project.project_expenses:
                cost_date = self._ensure_naive_datetime(cost.date)
                if cost_date < start_date:
                    total_anticipated_project_expenses += float(cost.amount)

        # Process purchase orders
        for po in purchase_orders:
            if po.due_date:
                payment_date = self._ensure_naive_datetime(po.due_date)
                if payment_date < start_date:
                    if po.project_id in current_project_ids:
                        total_current_project_purchase_orders += float(po.amount)
                    elif po.project_id in anticipated_project_ids:
                        total_anticipated_project_purchase_orders += float(po.amount)

        # Process invoices (income)
        for invoice in invoices:
            invoice_date = self._ensure_naive_datetime(invoice.due_date)
            if invoice_date < start_date:
                if invoice.project_id in current_project_ids:
                    total_current_project_invoices -= float(invoice.amount)
                elif invoice.project_id in anticipated_project_ids:
                    total_anticipated_project_invoices -= float(invoice.amount)

        return (
            total_current_project_income,
            total_anticipated_project_income,
            total_current_project_savings,
            total_anticipated_project_savings,
            total_current_project_expenses,
            total_anticipated_project_expenses,
            total_current_project_purchase_orders,
            total_anticipated_project_purchase_orders,
            total_current_project_invoices,
            total_anticipated_project_invoices,
        )

    async def generate_forecast(
        self, start_date: datetime, end_date: datetime, organization_id: int
    ) -> ForecastResponse:
        """Generate a new forecast."""
        try:
            # Ensure start_date and end_date are naive datetime objects
            if start_date.tzinfo is not None:
                start_date = start_date.replace(tzinfo=None)
            if end_date.tzinfo is not None:
                end_date = end_date.replace(tzinfo=None)

            # Create week dates (each week starts on Sunday)
            week_dates = []
            current_date = start_date
            while current_date <= end_date:
                week_dates.append(current_date)
                current_date += timedelta(days=7)

            # Initialize arrays
            num_weeks = len(week_dates)
            baseline_cash = [0.0] * num_weeks
            savings_balance = [0.0] * num_weeks
            accounts_totals = [0.0] * num_weeks
            payroll_expenses_totals = [0.0] * num_weeks
            fixed_expenses_totals = [0.0] * num_weeks
            misc_expenses_totals = [0.0] * num_weeks

            # Initialize project-specific cash flow arrays
            current_projects_expenses = [0.0] * num_weeks
            current_projects_purchase_orders = [0.0] * num_weeks
            current_projects_invoices = [0.0] * num_weeks
            anticipated_projects_expenses = [0.0] * num_weeks
            anticipated_projects_purchase_orders = [0.0] * num_weeks
            anticipated_projects_invoices = [0.0] * num_weeks

            # Get all required data using operations classes
            accounts = await self.account_ops.get_accounts(organization_id)
            fixed_expenses = await self.fixed_expense_ops.get_fixed_expenses(
                organization_id
            )
            misc_expenses = await self.misc_expense_ops.get_misc_expenses(
                organization_id
            )
            payroll_expenses = await self.payroll_expense_ops.get_payroll_expenses(
                organization_id
            )
            current_projects = await self.project_ops.get_current_projects(
                organization_id
            )
            anticipated_projects = await self.project_ops.get_anticipated_projects(
                organization_id
            )
            purchase_orders = await self.purchase_order_ops.get_purchase_orders(
                organization_id=organization_id
            )
            invoices = await self.invoice_ops.get_invoices(organization_id)

            # Calculate historical totals before start date
            historical_payroll, historical_fixed, historical_misc = (
                await self._calculate_historical_totals(organization_id, start_date)
            )
            (
                historical_current_project_income,
                historical_anticipated_project_income,
                historical_current_project_savings,
                historical_anticipated_project_savings,
                historical_current_project_expenses,
                historical_anticipated_project_expenses,
                historical_current_project_purchase_orders,
                historical_anticipated_project_purchase_orders,
                historical_current_project_invoices,
                historical_anticipated_project_invoices,
            ) = await self._calculate_historical_project_data(
                organization_id, start_date
            )

            # Initialize balances from accounts
            checking_account = next(
                (acc for acc in accounts if acc.account_type == "Checking"), None
            )
            savings_account = next(
                (acc for acc in accounts if acc.account_type == "Savings"), None
            )
            credit_card_account = next(
                (acc for acc in accounts if acc.account_type == "Credit Card"), None
            )
            line_of_credit_account = next(
                (acc for acc in accounts if acc.account_type == "Line of Credit"), None
            )

            if checking_account:
                baseline_cash[0] = float(checking_account.balance)
                accounts_totals[0] = float(checking_account.balance)

                # Initialize project cash flow arrays with baseline balance
                current_projects_expenses[0] = float(checking_account.balance)
                current_projects_purchase_orders[0] = float(checking_account.balance)
                current_projects_invoices[0] = float(checking_account.balance)
                anticipated_projects_expenses[0] = float(checking_account.balance)
                anticipated_projects_purchase_orders[0] = float(
                    checking_account.balance
                )
                anticipated_projects_invoices[0] = float(checking_account.balance)

            if credit_card_account:
                amount = float(credit_card_account.balance)
                baseline_cash[0] -= amount
                accounts_totals[0] -= amount

                # Apply credit card balance to project cash flow arrays
                current_projects_expenses[0] -= amount
                current_projects_purchase_orders[0] -= amount
                current_projects_invoices[0] -= amount
                anticipated_projects_expenses[0] -= amount
                anticipated_projects_purchase_orders[0] -= amount
                anticipated_projects_invoices[0] -= amount

            if line_of_credit_account:
                amount = float(line_of_credit_account.balance)
                baseline_cash[0] -= amount
                accounts_totals[0] -= amount

                # Apply line of credit balance to project cash flow arrays
                current_projects_expenses[0] -= amount
                current_projects_purchase_orders[0] -= amount
                current_projects_invoices[0] -= amount
                anticipated_projects_expenses[0] -= amount
                anticipated_projects_purchase_orders[0] -= amount
                anticipated_projects_invoices[0] -= amount

            if savings_account:
                savings_balance[0] = float(savings_account.balance)

            # Apply historical project savings to savings balance
            savings_balance[0] += (
                historical_current_project_savings
                + historical_anticipated_project_savings
            )

            # Apply historical totals to base values
            payroll_expenses_totals[0] = historical_payroll
            fixed_expenses_totals[0] = historical_fixed
            misc_expenses_totals[0] = historical_misc

            current_projects_expenses[0] -= historical_current_project_expenses
            current_projects_purchase_orders[
                0
            ] -= historical_current_project_purchase_orders
            current_projects_invoices[0] -= historical_current_project_invoices
            anticipated_projects_expenses[0] -= historical_anticipated_project_expenses
            anticipated_projects_purchase_orders[
                0
            ] -= historical_anticipated_project_purchase_orders
            anticipated_projects_invoices[0] -= historical_anticipated_project_invoices

            # Apply historical project data to accounts and cash flow
            # Net effect = income - (project expenses + purchase orders)
            historical_net_effect = (
                historical_current_project_income
                + historical_anticipated_project_income
                - historical_current_project_invoices
                - historical_anticipated_project_invoices
            )
            baseline_cash[0] += historical_net_effect
            accounts_totals[0] += historical_net_effect

            # Track project IDs for PO and invoice assignment
            current_project_ids = {proj.id for proj in current_projects}
            anticipated_project_ids = {proj.id for proj in anticipated_projects}

            # Process each week
            for i in range(num_weeks):
                week_start = week_dates[i]
                week_end = week_start + timedelta(days=6)

                # Carry forward previous week's balance (except for first week)
                if i > 0:
                    baseline_cash[i] = baseline_cash[i - 1]
                    savings_balance[i] = savings_balance[i - 1]
                    accounts_totals[i] = accounts_totals[i - 1]

                    # Carry forward expense totals arrays
                    payroll_expenses_totals[i] = payroll_expenses_totals[i - 1]
                    fixed_expenses_totals[i] = fixed_expenses_totals[i - 1]
                    misc_expenses_totals[i] = misc_expenses_totals[i - 1]

                    # Carry forward project cash flow arrays
                    current_projects_expenses[i] = current_projects_expenses[i - 1]
                    current_projects_purchase_orders[i] = (
                        current_projects_purchase_orders[i - 1]
                    )
                    current_projects_invoices[i] = current_projects_invoices[i - 1]
                    anticipated_projects_expenses[i] = anticipated_projects_expenses[
                        i - 1
                    ]
                    anticipated_projects_purchase_orders[i] = (
                        anticipated_projects_purchase_orders[i - 1]
                    )
                    anticipated_projects_invoices[i] = anticipated_projects_invoices[
                        i - 1
                    ]

                # Process fixed expenses
                for expense in fixed_expenses:
                    if self._should_apply_recurring_expense(
                        expense, week_start, week_end
                    ):
                        amount = float(expense.amount)
                        baseline_cash[i] -= amount
                        accounts_totals[i] -= amount
                        fixed_expenses_totals[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process misc expenses
                for expense in misc_expenses:
                    expense_date = self._ensure_naive_datetime(expense.date)
                    if expense_date >= week_start and expense_date <= week_end:
                        amount = float(expense.amount)
                        baseline_cash[i] -= amount
                        accounts_totals[i] -= amount
                        misc_expenses_totals[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process payroll expenses
                for expense in payroll_expenses:
                    if self._should_apply_recurring_expense(
                        expense, week_start, week_end
                    ):
                        amount = float(expense.amount)
                        baseline_cash[i] -= amount
                        accounts_totals[i] -= amount
                        payroll_expenses_totals[i] += amount

                        # Apply to project cash flow arrays
                        current_projects_expenses[i] -= amount
                        current_projects_purchase_orders[i] -= amount
                        current_projects_invoices[i] -= amount
                        anticipated_projects_expenses[i] -= amount
                        anticipated_projects_purchase_orders[i] -= amount
                        anticipated_projects_invoices[i] -= amount

                # Process current projects
                for project in current_projects:
                    # Process milestones - add to all project arrays (income)
                    for milestone in project.milestones:
                        milestone_date = self._ensure_naive_datetime(milestone.date)
                        if milestone_date >= week_start and milestone_date <= week_end:
                            amount = float(milestone.amount)
                            savings_amount = amount * (project.savings_percentage / 100)
                            savings_balance[i] += savings_amount

                            # Add milestone income to accounts totals and baseline cash
                            accounts_totals[i] += amount - savings_amount
                            baseline_cash[i] += amount - savings_amount

                            # Add milestone income to all project arrays
                            current_projects_expenses[i] += amount
                            current_projects_purchase_orders[i] += amount
                            current_projects_invoices[i] += amount

                            # Apply savings to project arrays
                            current_projects_expenses[i] -= savings_amount
                            current_projects_purchase_orders[i] -= savings_amount
                            current_projects_invoices[i] -= savings_amount

                    # Process project expenses - apply to expenses and subsequent arrays
                    for cost in project.project_expenses:
                        cost_date = self._ensure_naive_datetime(cost.date)
                        if cost_date >= week_start and cost_date <= week_end:
                            amount = float(cost.amount)

                            # Apply project expenses to accounts totals and baseline cash
                            # accounts_totals[i] -= amount
                            # baseline_cash[i] -= amount

                            # Apply project expenses to appropriate arrays
                            current_projects_expenses[i] -= amount

                # Process anticipated projects
                for project in anticipated_projects:
                    # Process milestones - add to anticipated project arrays only
                    for milestone in project.milestones:
                        milestone_date = self._ensure_naive_datetime(milestone.date)
                        if milestone_date >= week_start and milestone_date <= week_end:
                            amount = float(milestone.amount)
                            savings_amount = amount * (project.savings_percentage / 100)
                            savings_balance[i] += savings_amount

                            # Add milestone income to accounts totals and baseline cash
                            accounts_totals[i] += amount - savings_amount
                            baseline_cash[i] += amount - savings_amount

                            # Add milestone income to anticipated project arrays only
                            anticipated_projects_expenses[i] += amount
                            anticipated_projects_purchase_orders[i] += amount
                            anticipated_projects_invoices[i] += amount

                            # Apply savings to anticipated project arrays
                            anticipated_projects_expenses[i] -= savings_amount
                            anticipated_projects_purchase_orders[i] -= savings_amount
                            anticipated_projects_invoices[i] -= savings_amount

                    # Process project expenses - apply to anticipated arrays only
                    for cost in project.project_expenses:
                        cost_date = self._ensure_naive_datetime(cost.date)
                        if cost_date >= week_start and cost_date <= week_end:
                            amount = float(cost.amount)

                            # Apply project expenses to accounts totals and baseline cash
                            # accounts_totals[i] -= amount
                            # baseline_cash[i] -= amount

                            # Apply project expenses to anticipated arrays only
                            anticipated_projects_expenses[i] -= amount

                # Process purchase orders
                for po in purchase_orders:
                    # Use stored due_date
                    if po.due_date:
                        payment_date = self._ensure_naive_datetime(po.due_date)
                        if payment_date >= week_start and payment_date <= week_end:
                            amount = float(po.amount)

                            # Apply purchase order to accounts totals and baseline cash
                            # accounts_totals[i] -= amount
                            # baseline_cash[i] -= amount

                            if po.project_id in current_project_ids:
                                # Apply to current project purchase order and invoice arrays
                                current_projects_purchase_orders[i] -= amount

                            elif po.project_id in anticipated_project_ids:
                                # Apply to anticipated project purchase order and invoice arrays
                                anticipated_projects_purchase_orders[i] -= amount

                # Process invoices
                for invoice in invoices:
                    invoice_date = self._ensure_naive_datetime(invoice.due_date)
                    if invoice_date >= week_start and invoice_date <= week_end:
                        amount = float(invoice.amount)

                        # Apply invoice income to accounts totals and baseline cash
                        accounts_totals[i] -= amount
                        baseline_cash[i] -= amount

                        if invoice.project_id in current_project_ids:
                            # Apply to current project invoice array only
                            current_projects_invoices[i] += amount
                        elif invoice.project_id in anticipated_project_ids:
                            # Apply to anticipated project invoice array only
                            anticipated_projects_invoices[i] += amount

            # Convert project data to response format
            current_projects_response = [
                create_project_response(project) for project in current_projects
            ]
            anticipated_projects_response = {
                "projects": [
                    create_project_response(project) for project in anticipated_projects
                ],
                "archived": [],  # We don't need archived projects for forecast calculations
            }

            # Convert accounts to response format
            from app.cash_flow.models import AccountResponse

            accounts_response = [
                AccountResponse.model_validate(account) for account in accounts
            ]

            return ForecastResponse(
                week_dates=[d.strftime("%Y-%m-%d") for d in week_dates],
                baseline_cash=baseline_cash,
                savings_balance=savings_balance,
                accounts_totals=accounts_totals,
                payroll_expenses_totals=payroll_expenses_totals,
                fixed_expenses_totals=fixed_expenses_totals,
                misc_expenses_totals=misc_expenses_totals,
                current_projects_expenses=current_projects_expenses,
                current_projects_purchase_orders=current_projects_purchase_orders,
                current_projects_invoices=current_projects_invoices,
                anticipated_projects_expenses=anticipated_projects_expenses,
                anticipated_projects_purchase_orders=anticipated_projects_purchase_orders,
                anticipated_projects_invoices=anticipated_projects_invoices,
                current_projects=current_projects_response,
                anticipated_projects=anticipated_projects_response,
                accounts=accounts_response,
            )
        except Exception as e:
            raise ValueError(f"Failed to generate forecast: {str(e)}")
